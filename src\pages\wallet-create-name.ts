import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { WalletCreateFailedPage } from "./wallet-create-failed"
import { WalletCreateSuccessPage } from "./wallet-create-success"
import { WalletCreateWarnNameExistsPage } from "./wallet-create-warn-name-exists"
import { WalletCreateWarnNameInvalidPage } from "./wallet-create-warn-name-invalid"
import { WalletCreateWarnDuplicateAddressPage } from "./wallet-create-warn-duplicate-address"
import { Name } from "../name"

export class WalletCreateNamePage {
  private name = Name.walletCreateName
  private generalErrorPage: GeneralErrorPage
  private walletCreateFailedPage: WalletCreateFailedPage
  private walletCreateSuccessPage: WalletCreateSuccessPage
  private walletCreateWarnNameExistsPage: WalletCreateWarnNameExistsPage
  private walletCreateWarnNameInvalidPage: WalletCreateWarnNameInvalidPage
  private walletCreateWarnDuplicateAddressPage: WalletCreateWarnDuplicateAddressPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletCreateFailedPage = new WalletCreateFailedPage(this.handler)
    this.walletCreateSuccessPage = new WalletCreateSuccessPage(this.handler)
    this.walletCreateWarnNameExistsPage = new WalletCreateWarnNameExistsPage(this.handler)
    this.walletCreateWarnNameInvalidPage = new WalletCreateWarnNameInvalidPage(this.handler)
    this.walletCreateWarnDuplicateAddressPage = new WalletCreateWarnDuplicateAddressPage(this.handler)
  }

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.start)
    })
  }

  async show() {
    const chainName = this.handler.callbackDataParams
    const { chainSymbol, chainDisplayName } = BlockchainConfig.get(chainName as any)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { chainName }
    })

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard, {
      chainName: chainDisplayName,
      chainSymbol
    })
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { chainName } = sessionParams
      const { chainId } = BlockchainConfig.get(chainName) // Get chain ID from blockchain config
      if (input.length > 32 || !/^[a-zA-Z0-9_-]+$/.test(input)) {
        await this.walletCreateWarnNameInvalidPage.show()
        return
      }

      await this.handler.sessionDelete()
      const { address, privateKey } = BlockchainWallet.get(chainName).generate() // Generate wallet address and private key
      const wallet = await WalletModel.create(this.handler.userId, input, chainName, chainId, address, privateKey, `system`, this.handler.passwordWallet) // Create the wallet with encryption

      if (wallet && typeof wallet === `object`) this.walletCreateSuccessPage.show(chainName, wallet)
      else if (wallet === `DUPLICATE_NAME`) this.walletCreateWarnNameExistsPage.show()
      else if (wallet === `DUPLICATE_ADDRESS`) this.walletCreateWarnDuplicateAddressPage.show(chainName, address)
      else this.walletCreateFailedPage.show(chainName)
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
