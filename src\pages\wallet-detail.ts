import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Time } from "../utils/time"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailPage {
  private name = Name.walletDetail
  private walletGuard: WalletGuard
  constructor(private handler: Hand<PERSON>) {
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard()
      .text(`🔄 Refresh`, `${this.name}:${hex}`)
      .text(`📊 Position`, `${Name.walletDetailPosition}:${hex}`)
      .row()
      .text(`💸 Withdraw`, `${Name.walletDetailWithdraw}:${hex}`)
      .text(`🔑 Export`, `${Name.walletDetailExport}:${hex}`)
      .row()
      .text(`🗑️ Delete`, `${Name.walletDetailDelete}:${hex}`)
      .row()
      .text(`... Back`, Name.wallet)
      .text(`≡ Home`, Name.start)
  }

  async show() {
    await this.handler.sessionDelete() // Delete session if have
    const walletId = BigInt(this.handler.callbackDataParams)
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    const { chainId, chainDisplayName, chainSymbol, chainDecimals } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      chainName: chainDisplayName,
      walletName: wallet.name,
      walletAddress: wallet.address,
      walletBalance: BlockchainUtils.formatUnits(wallet.balance, chainDecimals),
      chainSymbol,
      chainId,
      walletCreatedDate: Time.format(wallet.createdAt),
      walletCreatedBy: wallet.createdBy,
      timestamp: Time.nowUnix().toString()
    })
  }
}
