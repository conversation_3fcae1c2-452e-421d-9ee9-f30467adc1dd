import { InlineKeyboard } from "grammy"
import type { Handler } from "../handler"
import { BlockchainWallet } from "../blockchain/wallet"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailWithdrawAmountPage } from "./wallet-detail-withdraw-amount"
import { WalletDetailWithdrawWarnInvalidAddressPage } from "./wallet-detail-withdraw-warn-invalid-address"
import { WalletDetailWithdrawWarnInvalidDestinationPage } from "./wallet-detail-withdraw-warn-invalid-destination"
import { WalletGuard } from "../guards/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawPage {
  private name = Name.walletDetailWithdrawAddress
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailWithdrawAmountPage: WalletDetailWithdrawAmountPage
  private walletDetailWithdrawWarnInvalidAddressPage: WalletDetailWithdrawWarnInvalidAddressPage
  private walletDetailWithdrawWarnInvalidDestinationPage: WalletDetailWithdrawWarnInvalidDestinationPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailWithdrawAmountPage = new WalletDetailWithdrawAmountPage(this.handler)
    this.walletDetailWithdrawWarnInvalidAddressPage = new WalletDetailWithdrawWarnInvalidAddressPage(this.handler)
    this.walletDetailWithdrawWarnInvalidDestinationPage = new WalletDetailWithdrawWarnInvalidDestinationPage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${hex}`)
  }

  async show() {
    const walletId = BigInt(this.handler.callbackDataParams)
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    // Set up session for address input
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { walletId, chainName: wallet.chain }
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: any, input: string) {
    try {
      const chainName = sessionParams.chainName
      const walletId = BigInt(sessionParams.walletId)
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      // Validate address format
      const blockchainWallet = BlockchainWallet.get(chainName)
      if (!blockchainWallet.isValidAddress(input)) {
        await this.walletDetailWithdrawWarnInvalidAddressPage.show(walletId)
        return
      }

      // Check if user is trying to send to themselves
      if (input.toLowerCase() === wallet.address.toLowerCase()) {
        await this.walletDetailWithdrawWarnInvalidDestinationPage.show(walletId)
        return
      }

      // Show amount page
      await this.walletDetailWithdrawAmountPage.show(walletId, input)
    } catch (error) {
      await this.generalErrorPage.show(`${this.name}:input`, error)
    }
  }
}
