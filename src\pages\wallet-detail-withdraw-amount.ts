import { InlineKeyboard } from "grammy"
import type { Handler } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailWithdrawConfirmPage } from "./wallet-detail-withdraw-confirm"
import { WalletDetailWithdrawWarnMinAmountPage } from "./wallet-detail-withdraw-warn-min-amount"
import { WalletDetailWithdrawWarnInvalidAmountPage } from "./wallet-detail-withdraw-warn-invalid-amount"
import { WalletDetailWithdrawFailedEstimateFeePage } from "./wallet-detail-withdraw-failed-estimate-fee"
import { WalletDetailWithdrawFailedInsufficientBalancePage } from "./wallet-detail-withdraw-failed-insufficient-balance"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailWithdrawAmountPage {
  private name = Name.walletDetailWithdrawAmount
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailWithdrawConfirmPage: WalletDetailWithdrawConfirmPage
  private walletDetailWithdrawWarnMinAmountPage: WalletDetailWithdrawWarnMinAmountPage
  private walletDetailWithdrawWarnInvalidAmountPage: WalletDetailWithdrawWarnInvalidAmountPage
  private walletDetailWithdrawFailedEstimateFeePage: WalletDetailWithdrawFailedEstimateFeePage
  private walletDetailWithdrawFailedInsufficientBalancePage: WalletDetailWithdrawFailedInsufficientBalancePage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailWithdrawConfirmPage = new WalletDetailWithdrawConfirmPage(this.handler)
    this.walletDetailWithdrawWarnInvalidAmountPage = new WalletDetailWithdrawWarnInvalidAmountPage(this.handler)
    this.walletDetailWithdrawWarnMinAmountPage = new WalletDetailWithdrawWarnMinAmountPage(this.handler)
    this.walletDetailWithdrawFailedEstimateFeePage = new WalletDetailWithdrawFailedEstimateFeePage(this.handler)
    this.walletDetailWithdrawFailedInsufficientBalancePage = new WalletDetailWithdrawFailedInsufficientBalancePage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${walletId}`).text(`🔄 Repeat`, `${Name.walletDetailWithdraw}:${hex}`)
  }

  async show(sessionParams: any, input: string) {
    const { chainName } = sessionParams
    const { chainSymbol } = BlockchainConfig.get(chainName)
    const walletId = BigInt(sessionParams.walletId)

    // Set up session for amount input
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { ...sessionParams, toAddress: input }
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      chainSymbol
    })
  }

  async input(sessionParams: any, input: string) {
    try {
      const { chainName, walletId } = sessionParams
      const { chainSymbol, chainDecimals } = BlockchainConfig.get(chainName)
      const amountInput = input.toLowerCase() === `max` ? `max` : isNaN(input as any) ? null : BlockchainUtils.parseUnits(input, chainDecimals)
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      // Parse amount input
      let amount = amountInput as bigint
      const currentBalance = wallet.balance
      if (amountInput === `max`) {
        // For max amount, we need to estimate fees first and subtract them
        const wallet = BlockchainWallet.get(chainName)
        const feeEstimate = await wallet.estimateFee(`${currentBalance}`)
        if (feeEstimate.error) {
          await this.walletDetailWithdrawFailedEstimateFeePage.show(wallet, `could not estimate network fees: ${feeEstimate.error}`)
          return
        }

        const maxSendable = currentBalance - BigInt(feeEstimate.result as any) || 0n
        if (maxSendable <= 0n) {
          await this.walletDetailWithdrawFailedInsufficientBalancePage.show(wallet, `insufficient balance to cover network fees. You need at least ${feeEstimate.result} ${chainSymbol} for transaction fees.`)
          return
        }

        amount = maxSendable
      } else if (amountInput === null || amountInput <= 0n) {
        // Only numeric input and higher than 0
        await this.walletDetailWithdrawWarnInvalidAmountPage.show(walletId)
        return
      } else {
        // Check minimum amount (0.001 for most chains) !!! FOR DISPLAY ONLY !!!
        const minAmount = BlockchainUtils.parseUnits(`0.001`, chainDecimals)
        if (amountInput < minAmount) {
          await this.walletDetailWithdrawWarnMinAmountPage.show(walletId)
          return
        }

        // Check if amount exceeds balance
        if (amountInput > currentBalance) {
          const formatAmount = BlockchainUtils.formatUnits(amountInput, chainDecimals)
          const formatCurrentBalance = BlockchainUtils.formatUnits(currentBalance, chainDecimals)
          await this.walletDetailWithdrawFailedInsufficientBalancePage.show(wallet, `insufficient balance. You have ${formatCurrentBalance} (${chainSymbol}) but trying to send ${formatAmount} (${chainSymbol}).`)
          return
        }
      }

      // Show confirmation page
      await this.walletDetailWithdrawConfirmPage.show(sessionParams, amount)
    } catch (error) {
      await this.generalErrorPage.show(`${this.name}:input`, error)
    }
  }
}
