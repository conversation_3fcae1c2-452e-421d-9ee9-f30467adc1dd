<b>⚙️ Your All Config</b>

<b>Total Config:</b> {{totalConfig}}
<b>Page:</b> {{currentPage}} of {{totalPages}}

<render>
    let str = ""
    const len = configList.length
    for (let index = 0; index < len; index++) {
        const wallet = configList[index]
        // tampilkan dari trade-config
        <!-- str += `${startIndex + index + 1}. ${wallet.name}\n🔗 ${wallet.chain}\n💰 ${wallet.balance} ${wallet.chainSymbol}\n📍 <code>${wallet.address}</code> ${(index === len - 1 ? `\n`: `\n\n`)}` -->
    }
    return str
</render>

<b>💡 Quick Actions:</b>
• Use <b>Create</b> to add new config
• Navigate with pagination buttons if you have many config

Select a config below to view details: