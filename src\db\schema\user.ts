import { sql } from "drizzle-orm"
import { boolean, index, integer, pgTable, timestamp, varchar } from "drizzle-orm/pg-core"

export const userSchema = pgTable(
  `user`,
  {
    id: integer().primaryKey(), // Telegram user ID as primary key
    username: varchar({ length: 32 }).notNull(), // Telegram username
    fullname: varchar({ length: 255 }).notNull(), // Telegram user fullname
    isActive: boolean().notNull().default(true), // User status
    fee: varchar({ length: 5 }).notNull().default(`0`), // Fee percentage for trading (e.g., "0.3" for 0.3%)
    language: varchar({ length: 2 }).notNull().default(`en`), // User's preferred language. ISO 3166-1 alpha-2.
    updatedAt: timestamp().defaultNow(),
    createdAt: timestamp().defaultNow()
  },
  (table) => [
    // Add index on username for faster searches
    index(`user_username_idx`).on(table.username),
    // Add index on isActive for faster filtering
    index(`user_is_active_idx`).on(table.isActive)
  ]
)
