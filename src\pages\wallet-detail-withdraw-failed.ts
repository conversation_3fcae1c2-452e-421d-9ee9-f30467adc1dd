import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"
import { ModelTrades } from "../db/models"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawFailedPage {
  private name = Name.walletDetailWithdrawFailed
  constructor(private handler: <PERSON><PERSON>) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletDetailWithdraw}:${hex}`).text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show(sessionParams: any, wallet: any, message: any) {
    const { chainName, toAddress, amount } = sessionParams
    const { chainDisplayName, chainSymbol } = BlockchainConfig.get(chainName)

    log.failed(`${this.name}: ${JSON.stringify(message)}`)
    await this.handler.sessionDelete()

    // Record failed transaction in history
    await ModelTrades.create(
      BigInt(wallet.id),
      chainName,
      false, // success = false
      amount,
      `S` // S for send/withdraw
    )

    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      toAddress,
      amount,
      chainSymbol,
      failedMessage: message
    })
  }
}
