import type { Hand<PERSON> } from "../handler"
import { WalletDetailExportFailedPage } from "./wallet-detail-export-failed"
import { WalletDetailExportSuccessPage } from "./wallet-detail-export-success"
import { BlockchainUtils } from "../blockchain/utils"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailExportExecutePage {
  private name = Name.walletDetailExportExecute
  private walletGuard: WalletGuard
  private walletDetailExportErrorPage: WalletDetailExportFailedPage
  private walletDetailExportSuccessPage: WalletDetailExportSuccessPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.walletDetailExportErrorPage = new WalletDetailExportFailedPage(this.handler)
    this.walletDetailExportSuccessPage = new WalletDetailExportSuccessPage(this.handler)
  }

  async show() {
    const exportWalletId = BigInt(this.handler.callbackDataParams)
    if (exportWalletId > 0) {
      const wallet = await this.walletGuard.ensureExists(exportWalletId)
      if (wallet === null) {
        return
      }

      wallet.privateKey = BlockchainUtils.decryptPrivateKey(wallet.privateKey, this.handler.passwordWallet)
      if (!wallet.privateKey) {
        return await this.walletDetailExportErrorPage.show(`Privatekey is not valid`, wallet)
      }
      await this.walletDetailExportSuccessPage.show(wallet)
    }
  }
}
