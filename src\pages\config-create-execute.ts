import type { <PERSON><PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { BlockchainWallet } from "../blockchain/wallet"
import { GeneralErrorSessionPage } from "./general-error-session"
import { GeneralErrorPage } from "./general-error"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"
import { ConfigCreateSuccessPage } from "./config-create-success"

export class ConfigCreateExecutePage {
  private name = Name.configCreateExecute
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private generalErrorSessionPage: GeneralErrorSessionPage
  private configCreateSuccessPage: ConfigCreateSuccessPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.generalErrorSessionPage = new GeneralErrorSessionPage(this.handler)
    this.configCreateSuccessPage = new ConfigCreateSuccessPage(this.handler)
  }

  async show() {
    const sessionData = await this.handler.sessionGet()
    if (!sessionData || sessionData.method !== `ck-${this.name}`) {
      await this.generalErrorSessionPage.show()
      return
    }
    // Clear the session
    await this.handler.sessionDelete()

    const sessionParams = sessionData.params
    const walletId = BigInt(sessionParams.walletId)
    const { inputAddress, inputAmount, inputRebuy, inputSell, inputMaxPendingSell } = sessionParams
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }
  }
}
