import { pgTable, varchar, bigint, integer, timestamp, text, smallint } from "drizzle-orm/pg-core"
import { walletSchema } from "./wallet"
import { sql } from "drizzle-orm"

export type TypeConfigInsert = typeof configSchema.$inferInsert
export const configSchema = pgTable(`config`, {
  id: varchar({ length: 25 }).notNull().primaryKey(), // config id
  owner: varchar({ length: 25 })
    .notNull()
    .references(() => walletSchema.id, { onDelete: `cascade` }), // wallet id as owner this config
  amount: bigint({ mode: `bigint` }).notNull(), // initial amount in
  path: text()
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`), // initial path address token
  percentRebuy: integer().notNull(), // percent rebuy
  percentSell: integer().notNull(), // percent sell
  maxPendingSell: integer().notNull(), // max count pending sell
  status: smallint().notNull(), // 0: pending, 1: success, 2: failed
  createdAt: timestamp().defaultNow(),
  updatedAt: timestamp().defaultNow()
})
