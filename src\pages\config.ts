import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { WalletGuard } from "../guards/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"
import { ConfigGuard } from "../guards/config"

export class ConfigPage {
  private name = Name.config
  private walletGuard: WalletGuard
  private configGuard: ConfigGuard
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.configGuard = new ConfigGuard(this.handler)
  }

  /**
   * Create wallet list keyboard with pagination
   * @param wallets Array of wallet objects
   * @param currentPage Current page number (0-based)
   * @param totalPages Total number of pages
   */
  createKeyboard(wallets: any[], currentPage: number, totalPages: number) {
    const keyboard = new InlineKeyboard()
    const prefixNext = Name.configDetail
    const prefixPagination = this.name

    // Add wallet buttons
    for (let index = 0; index < wallets.length; index++) {
      const wallet = wallets[index]
      const id = BlockchainUtils.toBeHex(wallet.id)
      keyboard.text(wallet.displayName, `${prefixNext}:${id}`).row()
    }

    // Add pagination if needed
    if (totalPages > 1) {
      keyboard.row()
      if (currentPage > 0) {
        keyboard.text(`⬅️ Previous`, `${prefixPagination}:${currentPage - 1}`)
      }
      keyboard.text(`${currentPage + 1}/${totalPages}`, `noop`)
      if (currentPage < totalPages - 1) {
        keyboard.text(`➡️ Next`, `${prefixPagination}:${currentPage + 1}`)
      }
    }

    // Add action buttons
    keyboard.row().text(`➕ Create`, Name.configCreateAddress).text(`... Back`, Name.start)
    return keyboard
  }

  async show() {
    await this.handler.sessionDelete()
    const part = this.handler.callbackData.split(`:`)[1] as string // dont use callbackDataParams because this spesific
    const page = parseInt(part === undefined ? `0` : part)
    const wallets = await this.walletGuard.ensureExistAll()

    const config = await this.configGuard
    if (wallets === null) {
      return
    }

    const walletsPerPage = 7
    const totalPages = Math.ceil(wallets.length / walletsPerPage)
    const currentPage = Math.max(0, Math.min(page, totalPages - 1))
    const startIndex = currentPage * walletsPerPage
    const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

    // Get wallets for current page with balance info
    const pageConfig = wallets.slice(startIndex, endIndex).map((wallet: any) => {
      const balance = wallet.balance.toString()
      const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
      const countConfig = 10
      return {
        ...wallet,
        chainSymbol,
        displayName: `??` //`${wallet.name} (${countConfig})`
      }
    })

    // menampikan text dengan konteks untuk input ke trade config
    const keyboard = this.createKeyboard(pageConfig, currentPage, totalPages)
    await this.handler.updateMsg(this.name, keyboard, {
      totalConfig: wallets.length,
      totalOwnerConfig: ``, // total owner wallet config
      currentPage: currentPage + 1,
      totalPages,
      startIndex,
      configList: pageConfig
    })
  }
}
