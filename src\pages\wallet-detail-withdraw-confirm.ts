import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailWithdrawFailedEstimateFeePage } from "./wallet-detail-withdraw-failed-estimate-fee"
import { WalletDetailWithdrawFailedInsufficientBalancePage } from "./wallet-detail-withdraw-failed-insufficient-balance"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailWithdrawConfirmPage {
  private name = Name.walletDetailWithdrawConfirm
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailWithdrawFailedEstimateFeePage: WalletDetailWithdrawFailedEstimateFeePage
  private walletDetailWithdrawFailedInsufficientBalancePage: WalletDetailWithdrawFailedInsufficientBalancePage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailWithdrawFailedEstimateFeePage = new WalletDetailWithdrawFailedEstimateFeePage(this.handler)
    this.walletDetailWithdrawFailedInsufficientBalancePage = new WalletDetailWithdrawFailedInsufficientBalancePage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`✅ Confirm Withdrawal`, `${Name.walletDetailWithdrawExecute}:${hex}`).text(`🔙 Repeat`, `${Name.walletDetailWithdraw}:${hex}`).row().text(`× Cancel`, `${Name.walletDetail}:${hex}`)
  }

  async show(sessionParams: Record<any, any>, amount: bigint) {
    try {
      const walletId = BigInt(this.handler.callbackDataParams)
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      // Estimate transaction fee
      const chainName = wallet.chain
      const { toAddress } = sessionParams
      const { chainDisplayName, chainSymbol } = BlockchainConfig.get(chainName as any)
      const transaction = BlockchainWallet.get(chainName)
      const feeEstimate = await transaction.estimateFee(`${amount}`)
      if (feeEstimate.error) {
        await this.walletDetailWithdrawFailedEstimateFeePage.show(wallet, `could not estimate network fees: ${feeEstimate.error}`)
        return
      }

      const estimatedFee = BigInt(feeEstimate.result || `0`)
      const totalAmount = amount + estimatedFee
      const currentBalance = wallet.balance
      const remainingBalance = currentBalance - totalAmount
      if (totalAmount > currentBalance) {
        // Check if user has enough balance including fees
        // const fTotalAmount = formatUnits(totalAmount, decimals)
        // const fCurrentBalance = formatUnits(currentBalance, decimals)
        // await this.walletDetailWithdrawFailedInsufficientBalancePage.show(wallet, `insufficient balance to cover amount + fees. Total needed: ${fTotalAmount} (${chainSymbol}), Available: ${fCurrentBalance} (${chainSymbol})`)
        return
      }

      // Just change method name, why use session? because callback_data can handle more than 64byte so for soluditon we use session
      await this.handler.sessionSet({
        method: `ck-${Name.walletDetailWithdrawExecute}`,
        params: sessionParams
      })

      const keyboard = this.createKeyboard(wallet.id)
      await this.handler.updateMsg(this.name, keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        fromAddress: wallet.address,
        toAddress,
        amount,
        chainSymbol,
        estimatedFee,
        totalAmount,
        remainingBalance
      })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
