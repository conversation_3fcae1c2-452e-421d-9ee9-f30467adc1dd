import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class ConfigCreateSuccessPage {
  private name = Name.configCreateSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return (
      new InlineKeyboard()
        //
        .text(`👁️ View Config`, `${Name.configDetail}:${hex}`)
        .text(`➕ Create Another`, `${Name.configCreateAddress}:${hex}`)
        .row()
        .text(`... Back`, Name.config)
        .text(`≡ Home`, Name.start)
    )
  }

  async show(wallet: any) {
    const { chainDisplayName } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      walletAddress: wallet.address,
      walletPrivateKey: wallet.privateKey
    })
  }
}
