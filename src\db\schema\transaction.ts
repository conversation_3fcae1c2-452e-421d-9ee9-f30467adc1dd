import { sql } from "drizzle-orm"
import { pgTable, varchar, bigint, timestamp, text, smallint } from "drizzle-orm/pg-core"

export const transactionSchema = pgTable(`transaction`, {
  hash: varchar({ length: 100 }).primaryKey(), // hash transaction
  from: varchar({ length: 50 }).notNull(), // sender transaction
  to: varchar({ length: 50 }).notNull(), // receiver transaction
  type: varchar({ length: 14 }).notNull(), // 'buy', 'sell', 'rebuy_up', 'rebuy_down', `transfer`, `transfer_token`
  amount: bigint({ mode: `bigint` }).notNull(), // amount/value transaction
  path: text()
    .array()
    .notNull()
    .default(sql`ARRAY[]::text[]`), // (contract) path address token to swap transaction
  status: smallint().notNull(), // 1: success, 2: failed
  chain: varchar({ length: 10 }).notNull(), // example: ethereum, solana
  walletId: varchar({ length: 25 }).notNull(), // wallet Id from schemaWallets.id
  timestamp: timestamp().defaultNow()
})
