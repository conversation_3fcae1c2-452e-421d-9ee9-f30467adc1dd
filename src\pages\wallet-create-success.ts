import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletCreateSuccessPage {
  private name = Name.walletCreateSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint, chainName: string) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`👁️ View Wallet`, `${Name.walletDetail}:${hex}`).text(`➕ Create Another`, `${Name.walletCreate}:${chainName}`).row().text(`... Back`, Name.wallet).text(`≡ Home`, Name.start)
  }

  async show(chainName: string, wallet: any) {
    const { chainDisplayName } = BlockchainConfig.get(chainName as any)
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard(wallet.id, chainName)
    await this.handler.replyMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      chainName: chainDisplayName
    })
  }
}
