import { InlineKeyboard } from "grammy"
import type { Handler } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { BlockchainUtils } from "../blockchain/utils"
import { WalletGuard } from "../guards/wallet"
import { ConfigCreateMaxPendingSellPage } from "./config-create-max-pending-sell"
import { Name } from "../name"

export class ConfigCreateSellPage {
  private name = Name.configCreateSell
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  private configCreateMaxPendingSellPage: ConfigCreateMaxPendingSellPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
    this.configCreateMaxPendingSellPage = new ConfigCreateMaxPendingSellPage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateAddress}:${hex}`)
  }

  async show(sessionParams: any, wallet: any) {
    const walletId = wallet.id
    const chainName = wallet.chain as any
    const { chainSymbol } = BlockchainConfig.get(chainName)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainSymbol
    })
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletId } = sessionParams
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      if (isNaN(input as any)) {
        await this.configCreateWarnInvalidAmountPage.show(walletId)
        return
      }

      await this.configCreateMaxPendingSellPage.show({ ...sessionParams, inputSell: input }, wallet)
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
