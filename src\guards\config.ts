import type { <PERSON><PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { UserModel } from "../db/models/user"
import { ConfigModel } from "../db/models/config"
import { ConfigDetailEmptyPage } from "../pages/config-detail-empty"
import { GeneralErrorPage } from "../pages/general-error"
import { UserBannedPage } from "../pages/user-banned"
import { UserCreateFailedPage } from "../pages/user-create-failed"
import { UserCreateSuccessPage } from "../pages/user-create-success"

export class ConfigGuard {
  private name = `ConfigGuard`
  private configDetailEmptyPage: ConfigDetailEmptyPage
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.configDetailEmptyPage = new ConfigDetailEmptyPage(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  async ensureExists(configId: string) {
    try {
      const config = await ConfigModel.getById(configId)
      if (config === null) {
        await this.configDetailEmptyPage.show()
        return null
      }
      const wallet = await WalletModel.getByIdForOwner(config.owner, this.handler.userId)
      if (wallet === null) {
        await this.configDetailEmptyPage.show()
        return null
      }

      return config
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }

  async ensureExistAll() {
    try {
      const wallets = await WalletModel.getAllForOwner(this.handler.userId)
      if (wallets.length === 0) {
        await this.walletEmptyPage.show()
        return null
      }
      return wallets
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }
}
