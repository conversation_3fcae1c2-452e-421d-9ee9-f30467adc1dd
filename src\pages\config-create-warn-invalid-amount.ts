import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class ConfigCreateWarnInvalidAmountPage {
  private name = Name.configCreateWarnInvalidAmount
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateAddress}:${hex}`)
  }

  async show(walletId: bigint) {
    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
