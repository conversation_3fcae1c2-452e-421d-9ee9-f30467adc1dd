import type { <PERSON><PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { BlockchainWallet } from "../blockchain/wallet"
import { GeneralErrorSessionPage } from "./general-error-session"
import { WalletDetailWithdrawFailedPage } from "./wallet-detail-withdraw-failed"
import { WalletDetailWithdrawSuccessPage } from "./wallet-detail-withdraw-success"
import { GeneralErrorPage } from "./general-error"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"
import { BlockchainUtils } from "../blockchain/utils"

export class WalletDetailWithdrawExecutePage {
  private name = Name.walletDetailWithdrawExecute
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private generalErrorSessionPage: GeneralErrorSessionPage
  private walletDetailWithdrawFailedPage: WalletDetailWithdrawFailedPage
  private walletDetailWithdrawSuccessPage: WalletDetailWithdrawSuccessPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.generalErrorSessionPage = new GeneralErrorSessionPage(this.handler)
    this.walletDetailWithdrawFailedPage = new WalletDetailWithdrawFailedPage(this.handler)
    this.walletDetailWithdrawSuccessPage = new WalletDetailWithdrawSuccessPage(this.handler)
  }

  async show() {
    const walletId = this.handler.callbackDataParams
    const sessionData = await this.handler.sessionGet()
    if (!sessionData || sessionData.method !== `ck-${this.name}`) {
      await this.generalErrorSessionPage.show()
      return
    }

    const sessionParams = sessionData.params
    const { toAddress, amount } = sessionParams
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    // Clear the session
    await this.handler.sessionDelete()

    try {
      // Get the decrypted private key for transaction signing
      const privateKey = BlockchainUtils.decryptPrivateKey(wallet.privateKey, this.handler.passwordWallet)
      if (!privateKey) {
        await this.generalErrorPage.show(this.name, `failed decrypt privatekey. walletid:${walletId}, userId:${this.handler.userId}`)
        return
      }

      // Execute the transaction
      const blockchainWallet = BlockchainWallet.get(wallet.chain)
      const tx = await blockchainWallet.send(privateKey, toAddress, amount)
      if (tx.error) {
        // Transaction failed
        await this.walletDetailWithdrawFailedPage.show(sessionParams, wallet, tx.error)
        return
      }

      const txHash = tx.result! // Transaction proof hash
      const newBalance = BigInt(wallet.balance - amount) // // Update wallet balance (refresh from blockchain or estimate). Rough estimate including fees
      const actualFee = `0.001` // Get actual fee from the transaction result (for display purposes)
      await WalletModel.updateBalance(walletId, this.handler.userId, newBalance > 0n ? newBalance : 0n) // Update wallet balance in database

      await this.walletDetailWithdrawSuccessPage.show(sessionParams, wallet, txHash, actualFee, newBalance)
    } catch (error) {
      await this.generalErrorPage.show(this.name, `throw error transaction: ${JSON.stringify(error)}`)
    }
  }
}
