import { bigint, integer, pgTable, timestamp, varchar, text, bigserial } from "drizzle-orm/pg-core"
import { userSchema } from "./user"

export const walletSchema = pgTable(`wallet`, {
  id: varchar({ length: 25 }).primaryKey(), // Wallet ID
  owner: integer()
    .notNull()
    .references(() => userSchema.id),
  name: varchar({ length: 32 }).notNull(), // Name of the wallet (max 32 bytes)
  chain: varchar({ length: 10 }).notNull(), // Example: ethereum, solana
  chainId: integer().notNull(), // Chain-specific ID
  address: varchar({ length: 50 }).notNull(), // Address of the wallet (max 60 bytes)
  balance: bigint({ mode: `bigint` }).notNull(), // Balance of the wallet
  privateKey: varchar({ length: 200 }).notNull(), // Encrypted private key of the wallet (max 200 bytes)
  createdBy: varchar({ length: 6 }).notNull().default(`system`), // Indicates how the wallet was created
  updatedAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
})
