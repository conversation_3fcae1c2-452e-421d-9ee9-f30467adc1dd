🤖 <b>Trading Bot Help</b>

<b>🔍 How the Trading Bot Works:</b>

<b>1. Initial Setup:</b>
• Enter token address you want to trade
• Set initial purchase amount (e.g., 1 SOL)
• Configure rebuy percentage (e.g., 10%)
• Configure sell percentage (e.g., 20%)
• Set maximum pending sell orders (e.g., 15)

<b>2. Trading Process:</b>
• <PERSON><PERSON> makes initial purchase with your SOL
• Creates sell order at +20% price increase
• Creates 2 rebuy orders at ±10% from last price
• When rebuy triggers, creates new sell order
• Process repeats automatically

<b>3. Safety Features:</b>
• Maximum pending orders limit prevents losses
• <PERSON><PERSON> pauses when limit reached
• All orders are monitored in real-time
• You can stop trading anytime

<b>4. Monitoring:</b>
• View real-time trading status
• Track success rates and performance
• Monitor pending orders and balances
• Pause/resume trading as needed

<b>⚠️ Important Notes:</b>
• Trading involves risk - only use funds you can afford to lose
• Market volatility can affect performance
• <PERSON><PERSON> works best with liquid tokens
• Always monitor your positions

<b>📊 Example Scenario:</b>
Initial: 1 SOL → 100,000 tokens
Sell Target: +20% = 120,000 tokens value
Rebuy: ±10% from last purchase price
