import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportWarnNameInvalidPage {
  private name = Name.walletImportWarnNameInvalid
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(WalletImportWarnNameInvalidPage.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.walletImport)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard)
  }
}
