import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"
import { ConfigGuard } from "../guards/config"
import { WalletGuard } from "../guards/wallet"

export class ConfigDetailPage {
  private name = Name.configDetail
  private configGuard: ConfigGuard
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(configId: bigint) {
    const hex = BlockchainUtils.toBeHex(configId)
    return (
      new InlineKeyboard()
        //
        .text(`🔄 Refresh`, `${this.name}:${hex}`)
        .text(`📊 Update`, `${Name.walletDetailPosition}:${hex}`)
        .row()
        .text(`🗑️ Delete`, `${Name.configDetailDelete}:${hex}`)
        .row()
        .text(`... Back`, Name.config)
        .text(`≡ Home`, Name.start)
    )
  }

  async show() {
    const configId = BigInt(this.handler.callbackDataParams)
    const config = await this.configGuard.ensureExists(configId)
    if (config === null) {
      return
    }

    const wallet = await this.walletGuard.ensureExists(config.owner)
    if (wallet === null) {
      return
    }

    const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(config.id)
    await this.handler.updateMsg(this.name, keyboard, {
      configId: config.id,
      configOwner: config.owner,
      configAmount: config.amount,
      configAddress: config.path[1],
      configPercentRebuy: config.percentRebuy,
      configPercentSell: config.percentSell,
      configMaxPendingSell: config.maxPendingSell,
      configStatus: config.status,
      configCreatedAt: config.createdAt,
      configUpdatedAt: config.updatedAt,
      chainSymbol
    })
  }
}
