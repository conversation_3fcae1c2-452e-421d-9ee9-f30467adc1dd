import type { <PERSON><PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { GeneralErrorPage } from "../pages/general-error"
import { WalletDetailEmptyPage } from "../pages/wallet-detail-empty"
import { WalletEmptyPage } from "../pages/wallet-empty"

export class WalletGuard {
  private name = `WalletGuard`
  private walletDetailEmptyPage: WalletDetailEmptyPage
  private walletEmptyPage: WalletEmptyPage
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.walletDetailEmptyPage = new WalletDetailEmptyPage(this.handler)
    this.walletEmptyPage = new WalletEmptyPage(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  async ensureExists(walletId: string) {
    try {
      const wallet = await WalletModel.getByIdForOwner(walletId, this.handler.userId)
      if (wallet === null) {
        await this.walletDetailEmptyPage.show(wallet)
        return null
      }
      return wallet
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }

  async ensureExistAll() {
    try {
      const wallets = await WalletModel.getAllForOwner(this.handler.userId)
      if (wallets.length === 0) {
        await this.walletEmptyPage.show()
        return null
      }
      return wallets
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }
}
