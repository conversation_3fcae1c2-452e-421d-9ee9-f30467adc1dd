import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class ConfigCreateConfirmPage {
  private name = Name.configCreateConfirm
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`✅ Ok`, Name.configCreateExecute).row().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateAddress}:${hex}`)
  }

  async show(sessionParams: any, wallet: any) {
    const walletId = wallet.id
    const chainName = wallet.chain as any
    const { chainSymbol } = BlockchainConfig.get(chainName)

    // Just change method name, why use session? because callback_data can handle more than 64byte so for soluditon we use session
    await this.handler.sessionSet({
      method: `ck-${Name.configCreateExecute}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      ...sessionParams,
      walletName: wallet.name,
      chainSymbol
    })
  }
}
