import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { log } from "../utils/log"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailExportFailedPage {
  private name = Name.walletDetailExportFailed
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show(message: any, wallet: any) {
    log.error(`${this.name}: ${JSON.stringify(message)}`)
    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletAddress: wallet.address
    })
  }
}
