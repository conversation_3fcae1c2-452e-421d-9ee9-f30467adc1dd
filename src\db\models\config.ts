import { and, eq, sql } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { configSchema, type TypeConfigInsert } from "../schema/config"

export class ConfigModel {
  private static name = `ConfigModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  /** Get config by ID */
  static async getById(id: string) {
    try {
      const [row] = await db.select().from(configSchema).where(eq(configSchema.id, id)).limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getById`)
    }
  }

  /** Get config by ID and ownerId */
  static async getByIdForOwner(id: string, ownerId: string) {
    try {
      const [row] = await db
        .select()
        .from(configSchema)
        .where(and(eq(configSchema.id, id), eq(configSchema.owner, ownerId)))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByIdForOwner`)
    }
  }

  /** Get all configs for a wallet owner */
  static async getByOwner(ownerId: string) {
    try {
      return await db.select().from(configSchema).where(eq(configSchema.owner, ownerId))
    } catch (error) {
      return this.logError(error, `getByOwner`, [])
    }
  }

  /** Get all config by ID */
  static async getAllById(id: string, limit?: number) {
    try {
      const smt = db.select().from(configSchema).where(eq(configSchema.id, id))
      if (limit !== undefined) {
        smt.limit(limit)
      }
      return await smt
    } catch (error) {
      return this.logError(error, `getAllById`, [])
    }
  }

  /** Create new trade config */
  static async create(data: TypeConfigInsert) {
    try {
      const [row] = await db
        .insert(configSchema)
        .values({ ...data, status: data.status ?? 0 })
        .returning()
      return row || null
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /** Update config status */
  static async updateStatus(id: string, status: number) {
    try {
      const [row] = await db.update(configSchema).set({ status }).where(eq(configSchema.id, id)).returning()
      return row || null
    } catch (error) {
      return this.logError(error, `updateStatus`)
    }
  }

  /** Delete config by ID */
  static async remove(id: string, ownerId: string) {
    try {
      await db.delete(configSchema).where(and(eq(configSchema.id, id), eq(configSchema.owner, ownerId)))
      return true
    } catch (error) {
      return this.logError(error, `delete`, false)
    }
  }
}
