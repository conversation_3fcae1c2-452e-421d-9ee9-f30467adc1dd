import { integer, pgTable, serial, text, timestamp, varchar } from "drizzle-orm/pg-core"

export const feeSchema = pgTable(`fee`, {
  id: serial().primaryKey(),
  chain: text().notNull(), // Chain type
  chainId: integer().notNull().default(0), // Chain-specific ID
  fee: varchar({ length: 10 }).notNull().default(`0`), // Fee percentage for trading (e.g., "0.3" for 0.3%)
  receiver: varchar({ length: 110 }).notNull(), // Address/PublicKey of the receiver
  updatedAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
})
