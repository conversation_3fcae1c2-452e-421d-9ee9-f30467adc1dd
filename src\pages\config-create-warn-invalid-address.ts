import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class ConfigCreateWarnInvalidAddressPage {
  private name = Name.configCreateWarnInvalidAddress
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard() {
    return new InlineKeyboard().text(`× Cancel`, Name.config)
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
