import { eq } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { adminSchema } from "../schema/admin"
import { userSchema } from "../schema/user"

export class AdminModel {
  private static name = `AdminModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  /**
   * Get an admin by user ID
   * @param id User ID
   * @returns The admin with user info or null if not found
   */
  static async getById(id: number) {
    try {
      const result = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))
        .where(eq(adminSchema.id, id))
        .limit(1)

      return result[0] || null
    } catch (error) {
      return this.logError(error, `getById`)
    }
  }

  /**
   * Get an admin by username
   * @param username Telegram username
   * @returns The admin with user info or null if not found
   */
  static async getByUsername(username: string) {
    try {
      const result = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))
        .where(eq(userSchema.username, username))
        .limit(1)

      return result[0] || null
    } catch (error) {
      return this.logError(error, `getByUsername`)
    }
  }

  /**
   * Create a new admin
   * @param id User ID (must exist in users table)
   * @param role Admin role (support, super)
   * @returns The created admin or null if failed
   */
  static async create(id: number, role: string = `support`) {
    try {
      // Check if user exists
      const user = await db.select().from(userSchema).where(eq(userSchema.id, id)).limit(1)
      if (!user[0]) {
        throw `User with ID ${id} does not exist`
      }

      const [row] = await db.insert(adminSchema).values({ id, role }).returning()
      return row || null
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /**
   * Get all admins with user information
   * @returns Array of admins with user info
   */
  static async getAll() {
    try {
      const admins = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))

      return admins
    } catch (error) {
      return this.logError(error, `getAll`, [])
    }
  }

  /**
   * Get all active admins
   * @returns Array of active admins with user info
   */
  static async getAllActive() {
    try {
      const admins = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))
        .where(eq(userSchema.isActive, true))

      return admins
    } catch (error) {
      return this.logError(error, `getAllActive`, [])
    }
  }

  /**
   * Check if user is an admin
   * @param id User ID
   * @returns True if user is admin, false otherwise
   */
  static async isAdmin(id: number): Promise<boolean> {
    try {
      const admin = await db.select().from(adminSchema).where(eq(adminSchema.id, id)).limit(1)
      return !!admin[0]
    } catch (error) {
      return this.logError(error, `isAdmin`, false)
    }
  }

  /**
   * Remove admin privileges
   * @param id User ID
   * @returns True if removed successfully, false otherwise
   */
  static async remove(id: number): Promise<boolean> {
    try {
      const result = await db.delete(adminSchema).where(eq(adminSchema.id, id)).returning()
      return !!result[0]
    } catch (error) {
      return this.logError(error, `remove`, false)
    }
  }

  /**
   * Update admin record timestamp
   * @param id User ID
   * @returns Updated admin or null if failed
   */
  static async updateTimestamp(id: number) {
    try {
      const updatedAdmin = await db
        .update(adminSchema)
        .set({
          updatedAt: new Date()
        })
        .where(eq(adminSchema.id, id))
        .returning()

      return updatedAdmin[0] || null
    } catch (error) {
      return this.logError(error, `updateTimestamp`)
    }
  }

  /**
   * Get admins by role
   * @param role Admin role (support, super)
   * @returns Array of admins with specified role
   */
  static async getByRole(role: string) {
    try {
      const admins = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))
        .where(eq(adminSchema.role, role))

      return admins
    } catch (error) {
      return this.logError(error, `getByRole`, [])
    }
  }

  /**
   * Get first admin by role
   * @param role Admin role (support, super)
   * @returns The first admin with specified role or null if not found
   */
  static async getFirstByRole(role: string) {
    try {
      const result = await db
        .select({
          id: adminSchema.id,
          role: adminSchema.role,
          username: userSchema.username,
          fullname: userSchema.fullname,
          isActive: userSchema.isActive,
          language: userSchema.language,
          adminCreatedAt: adminSchema.createdAt,
          adminUpdatedAt: adminSchema.updatedAt,
          userCreatedAt: userSchema.createdAt,
          userUpdatedAt: userSchema.updatedAt
        })
        .from(adminSchema)
        .innerJoin(userSchema, eq(adminSchema.id, userSchema.id))
        .where(eq(adminSchema.role, role))
        .limit(1)

      return result[0] || null
    } catch (error) {
      return this.logError(error, `getFirstByRole`)
    }
  }

  /**
   * Update admin role
   * @param id User ID
   * @param role New role (support, super)
   * @returns Updated admin or null if failed
   */
  static async updateRole(id: number, role: string) {
    try {
      const updatedAdmin = await db
        .update(adminSchema)
        .set({
          role,
          updatedAt: new Date()
        })
        .where(eq(adminSchema.id, id))
        .returning()

      return updatedAdmin[0] || null
    } catch (error) {
      return this.logError(error, `updateRole`)
    }
  }

  /**
   * Check if admin has super role
   * @param id User ID
   * @returns True if admin has super role, false otherwise
   */
  static async isSuper(id: number): Promise<boolean> {
    try {
      const admin = await db.select().from(adminSchema).where(eq(adminSchema.id, id)).limit(1)
      return admin[0]?.role === `super`
    } catch (error) {
      return this.logError(error, `isSuper`, false)
    }
  }
}
