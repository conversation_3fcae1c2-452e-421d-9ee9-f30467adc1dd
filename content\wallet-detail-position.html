<b>📊 Position trade for {{walletName}}</b>

<render>
    let str = ""
    const len = tradeList.length
    for (let index = 0; index < len; index++) {
        const context = tradeList[index];
        const operation = context.operation === `B` ? `🟢 BUY` : `🔴 SELL`
        const status = context.success ? `✅` : "❌"
        const amount = context.amount.toString()
        const date = context.createdAt || `Unknown`
        const id = index + 1
        str += `${id}. ${operation} ${status}\n💰 ${amount} ${symbol}\n📅 ${date}${(id === len ? `\n`: `\n\n`)}`
    }
    return str
</render>