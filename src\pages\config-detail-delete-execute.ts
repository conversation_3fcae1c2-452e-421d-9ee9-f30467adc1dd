import type { <PERSON><PERSON> } from "../handler"
import { ConfigModel } from "../db/models/config"
import { ConfigGuard } from "../guards/config"
import { Name } from "../name"
import { ConfigDetailDeleteFailedPage } from "./config-detail-delete-failed"
import { ConfigDetailDeleteSuccessPage } from "./config-detail-delete-success"

export class ConfigDetailDeleteExecutePage {
  private name = Name.configDetailDeleteExecute
  private configGuard: ConfigGuard
  private configDetailDeleteFailedPage: ConfigDetailDeleteFailedPage
  private configDetailDeleteSuccessPage: ConfigDetailDeleteSuccessPage
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.configDetailDeleteFailedPage = new ConfigDetailDeleteFailedPage(this.handler)
    this.configDetailDeleteSuccessPage = new ConfigDetailDeleteSuccessPage(this.handler)
  }

  async show() {
    const configId = BigInt(this.handler.callbackDataParams)
    const config = await this.configGuard.ensureExists(configId)
    if (config === null) {
      return
    }

    const success = await ConfigModel.remove(configId, config.owner)
    if (success) {
      await this.configDetailDeleteSuccessPage.show()
    } else {
      await this.configDetailDeleteFailedPage.show()
    }
  }
}
