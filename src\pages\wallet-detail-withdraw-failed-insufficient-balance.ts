import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { log } from "../utils/log"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawFailedInsufficientBalancePage {
  private name = Name.walletDetailWithdrawFailedInsufficientBalance
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletDetailWithdraw}:${hex}`).text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show(wallet: any, message: any) {
    log.failed(`${this.name}: ${JSON.stringify(message)}`)
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
