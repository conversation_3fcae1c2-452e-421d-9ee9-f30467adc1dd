import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateRebuyPage } from "./config-create-rebuy"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigCreateAmountPage {
  private name = Name.configCreateAmount
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateRebuyPage: ConfigCreateRebuyPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateRebuyPage = new ConfigCreateRebuyPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
  }

  createKeyboard(walletId: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateAddress}:${walletId}`)
  }

  async show(sessionParams: any, wallet: any) {
    const walletId = wallet.id
    const chainName = wallet.chain as any
    const { chainSymbol } = BlockchainConfig.get(chainName)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainSymbol
    })
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletId } = sessionParams
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      if (isNaN(input as any)) {
        await this.configCreateWarnInvalidAmountPage.show(walletId)
        return
      }

      await this.configCreateRebuyPage.show({ ...sessionParams, inputAmount: input }, wallet)
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
