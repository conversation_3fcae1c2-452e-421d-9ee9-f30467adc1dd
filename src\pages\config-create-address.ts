import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { BlockchainWallet } from "../blockchain/wallet"
import { ConfigCreateWarnInvalidAddressPage } from "./config-create-warn-invalid-address"
import { WalletGuard } from "../guards/wallet"
import { ConfigCreateAmountPage } from "./config-create-amount"
import { Name } from "../name"

export class ConfigCreateAddressPage {
  private name = Name.configCreateAddress
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateAmountPage: ConfigCreateAmountPage
  private configCreateWarnInvalidAddressPage: ConfigCreateWarnInvalidAddressPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateAmountPage = new ConfigCreateAmountPage(this.handler)
    this.configCreateWarnInvalidAddressPage = new ConfigCreateWarnInvalidAddressPage(this.handler)
  }

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.config)
    })
  }

  async show() {
    const walletId = BigInt(this.handler.callbackDataParams)
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { walletId }
    })

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainSymbol
    })
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { walletId } = sessionParams
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      const isAddress = BlockchainWallet.get(wallet.chain).isValidAddress(input)
      if (!isAddress) {
        await this.configCreateWarnInvalidAddressPage.show()
        return
      }

      await this.configCreateAmountPage.show({ ...sessionParams, inputAddress: input }, wallet)
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
