<b>⚙️ Your All Owner Config</b>

<b>Total:</b> {{totalConfig}}
<b>Total Owner:</b> {{totalOwnerConfig}} (wallet)
<b>Page:</b> {{currentPage}} of {{totalPages}}

<render>
    let str = ""
    const len = walletList.length
    for (let index = 0; index < len; index++) {
        const wallet = walletList[index]
        str += `${startIndex + index + 1}. ${wallet.name} (${wallet.chainSymbol})\ntotal: ${wallet.totalConfig} ${(index === len - 1 ? `\n`: `\n\n`)}`
    }
    return str
</render>

<b>💡 Quick Actions:</b>
• Navigate with pagination buttons if you have many config

Select the wallet below as the owner config that will be created: