import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { ModelTrades } from "../db/models"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailPositionEmptyPage } from "./wallet-detail-position-empty"
import { WalletGuard } from "../guards/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailPositonPage {
  private name = Name.walletDetailPosition
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailPositionEmptyPage: WalletDetailPositionEmptyPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailPositionEmptyPage = new WalletDetailPositionEmptyPage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show() {
    const walletId = BigInt(this.handler.callbackDataParams)
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    try {
      const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
      const tradeList = await ModelTrades.getForWallet(BigInt(walletId), 10)
      const keyboard = this.createKeyboard(walletId)
      if (tradeList.length === 0) {
        await this.walletDetailPositionEmptyPage.show(wallet)
        return
      }

      await this.handler.updateMsg(this.name, keyboard, {
        walletName: wallet.name,
        tradeList: tradeList,
        chainSymbol
      })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
