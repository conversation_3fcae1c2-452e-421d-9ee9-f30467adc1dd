export class Name {
  static adminAccessDenied = `admin-access-denied`
  static admin = `admin`
  static configCreateAddress = `config-create-address`
  static configCreateAmount = `trade-create-amount`
  static configCreateConfirm = `config-create-confirm`
  static configCreateExecute = `config-create-execute`
  static configCreateFailed = `config-create-failed`
  static configCreateMaxPendingSell = `config-create-max-pending-sell`
  static configCreateRebuy = `config-create-rebuy`
  static configCreateSell = `config-create-sell`
  static configCreateSuccess = `config-create-success`
  static configCreateWarnInvalidAddress = `config-create-warn-invalid-address`
  static configCreateWarnInvalidAmount = `config-create-warn-invalid-amount`
  static configCreate = `config-create`
  static configDetail = `config-detail`
  static configDetailDeleteExecute = `config-detail-delete-execute`
  static configDetailDeleteFailed = `config-detail-delete-failed`
  static configDetailDeleteSuccess = `config-detail-delete-success`
  static configDetailDelete = `config-detail-delete`
  static configDetailEmpty = `config-detail-empty`
  static config = `config`
  static generalErrorInputNotfound = `general-error-input-notfound`
  static generalErrorSession = `general-error-session`
  static generalError = `general-error`
  static generalFailed = `general-failed`
  static help = `help`
  static language = `language`
  static referral = `referral`
  static setting = `setting`
  static start = `start`
  static userBanned = `user-banned`
  static userCreateFailed = `user-create-failed`
  static userCreateSuccess = `user-create-success`
  static walletCreateFailed = `wallet-create-failed`
  static walletCreateName = `wallet-create-name`
  static walletCreateSuccess = `wallet-create-success`
  static walletCreateWarnDuplicateAddress = `wallet-create-warn-duplicate-address`
  static walletCreateWarnNameExists = `wallet-create-warn-name-exists`
  static walletCreateWarnNameInvalid = `wallet-create-warn-name-invalid`
  static walletCreate = `wallet-create`
  static walletDetailDeleteExecute = `wallet-detail-delete-execute`
  static walletDetailDeleteFailed = `wallet-detail-delete-failed`
  static walletDetailDeleteSuccess = `wallet-detail-delete-success`
  static walletDetailDelete = `wallet-detail-delete`
  static walletDetailEmpty = `wallet-detail-empty`
  static walletDetailExportExecute = `wallet-detail-export-execute`
  static walletDetailExportFailed = `wallet-detail-export-failed`
  static walletDetailExportSuccess = `wallet-detail-export-success`
  static walletDetailExport = `wallet-detail-export`
  static walletDetailPositionEmpty = `wallet-detail-position-empty`
  static walletDetailPosition = `wallet-detail-position`
  static walletDetailWithdrawAddress = `wallet-detail-withdraw-address`
  static walletDetailWithdrawAmount = `wallet-detail-withdraw-amount`
  static walletDetailWithdrawConfirm = `wallet-detail-withdraw-confirm`
  static walletDetailWithdrawExecute = `wallet-detail-withdraw-execute`
  static walletDetailWithdrawFailedEstimateFee = `wallet-detail-withdraw-failed-estimate-fee`
  static walletDetailWithdrawFailedInsufficientBalance = `wallet-detail-withdraw-failed-insufficient-balance`
  static walletDetailWithdrawFailed = `wallet-detail-withdraw-failed`
  static walletDetailWithdrawSuccess = `wallet-detail-withdraw-success`
  static walletDetailWithdrawWarnInvalidAddress = `wallet-detail-withdraw-warn-invalid-address`
  static walletDetailWithdrawWarnInvalidAmount = `wallet-detail-withdraw-warn-invalid-amount`
  static walletDetailWithdrawWarnInvalidDestination = `wallet-detail-withdraw-warn-invalid-destination`
  static walletDetailWithdrawWarnMinAmount = `wallet-detail-withdraw-warn-min-amount`
  static walletDetailWithdraw = `wallet-detail-withdraw`
  static walletDetail = `wallet-detail`
  static walletEmpty = `wallet-empty`
  static walletImportFailed = `wallet-import-failed`
  static walletImportName = `wallet-import-name`
  static walletImportPrivateKey = `wallet-import-private-key`
  static walletImportSuccess = `wallet-import-success`
  static walletImportWarnDuplicateAddress = `wallet-import-warn-duplicate-address`
  static walletImportWarnNameExists = `wallet-import-warn-name-exists`
  static walletImportWarnNameInvalid = `wallet-import-warn-name-invalid`
  static walletImportWarnPrivateKeyInvalid = `wallet-import-warn-private-key-invalid`
  static walletImport = `wallet-import`
  static wallet = `wallet`
}
