import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateSellPage } from "./config-create-sell"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { BlockchainUtils } from "../blockchain/utils"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigCreateRebuyPage {
  private name = Name.configCreateRebuy
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateSellPage: ConfigCreateSellPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateSellPage = new ConfigCreateSellPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateAddress}:${hex}`)
  }

  async show(sessionParams: any, wallet: any) {
    const walletId = wallet.id
    const chainName = wallet.chain as any
    const { chainSymbol } = BlockchainConfig.get(chainName)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainSymbol
    })
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { walletId } = sessionParams
      const wallet = await this.walletGuard.ensureExists(walletId)
      if (wallet === null) {
        return
      }

      if (isNaN(input as any)) {
        await this.configCreateWarnInvalidAmountPage.show(walletId)
        return
      }

      await this.configCreateSellPage.show({ ...sessionParams, inputRebuy: input }, wallet)
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
