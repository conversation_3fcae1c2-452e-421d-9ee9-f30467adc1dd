import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawWarnInvalidDestinationPage {
  private name = Name.walletDetailWithdrawWarnInvalidDestination
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${hex}`).text(`🔄 Repeat`, `${Name.walletDetailWithdraw}:${hex}`)
  }

  async show(walletId: bigint) {
    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
