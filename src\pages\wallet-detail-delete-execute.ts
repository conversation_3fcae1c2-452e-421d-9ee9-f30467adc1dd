import type { <PERSON><PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { WalletDetailDeleteFailedPage } from "./wallet-detail-delete-failed"
import { WalletDetailDeleteSuccessPage } from "./wallet-detail-delete-success"
import { Name } from "../name"

export class WalletDetailDeleteExecutePage {
  private name = Name.walletDetailDeleteExecute
  private walletDetailDeleteFailedPage: WalletDetailDeleteFailedPage
  private walletDetailDeleteSuccessPage: WalletDetailDeleteSuccessPage
  constructor(private handler: Handler) {
    this.walletDetailDeleteFailedPage = new WalletDetailDeleteFailedPage(this.handler)
    this.walletDetailDeleteSuccessPage = new WalletDetailDeleteSuccessPage(this.handler)
  }

  async show() {
    const walletId = this.handler.callbackDataParams
    const success = await WalletModel.remove(walletId, this.handler.userId)
    if (success) {
      await this.walletDetailDeleteSuccessPage.show()
    } else {
      await this.walletDetailDeleteFailedPage.show()
    }
  }
}
