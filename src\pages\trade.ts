import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class TradePage {
  private name = Name.config
  constructor(private handler: Handler) {}

  createKeyboard() {
    return new InlineKeyboard().text(`➕ Create`, Name.config).text(`... Back`, Name.start)
  }

  async show() {
    // menampilkan semua trade order dari user id
    await this.handler.updateMsg(this.name)
  }
}
