import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { ConfigGuard } from "../guards/config"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigDetailDeletePage {
  private name = Name.configDetailDelete
  private configGuard: ConfigGuard
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(configId: bigint) {
    const hex = BlockchainUtils.toBeHex(configId)
    return new InlineKeyboard().text(`... Back`, `${Name.configDetail}:${hex}`).text(`✅ Yes, Delete`, `${Name.configDetailDeleteExecute}:${hex}`).row()
  }

  async show() {
    const configId = BigInt(this.handler.callbackDataParams)
    const config = await this.configGuard.ensureExists(configId)
    if (config === null) {
      return
    }

    const wallet = await this.walletGuard.ensureExists(config.owner)
    if (wallet === null) {
      return
    }

    const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(configId)
    await this.handler.updateMsg(this.name, keyboard, {
      configId: config.id,
      configOwner: config.owner,
      chainSymbol
    })
  }
}
