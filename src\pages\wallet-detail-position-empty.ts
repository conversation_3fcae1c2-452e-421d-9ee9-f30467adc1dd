import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailPositionEmptyPage {
  private name = Name.walletDetailPositionEmpty
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show(wallet: any) {
    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name
    })
  }
}
