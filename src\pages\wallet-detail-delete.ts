import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { WalletGuard } from "../guards/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailDeletePage {
  private name = Name.walletDetailDelete
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${hex}`).text(`✅ Yes, Delete`, `${Name.walletDetailDeleteExecute}:${hex}`).row()
  }

  async show() {
    const walletId = BigInt(this.handler.callbackDataParams)
    const wallet = await this.walletGuard.ensureExists(walletId)
    if (wallet === null) {
      return
    }

    const { chainDisplayName } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(walletId)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      chainName: chainDisplayName
    })
  }
}
