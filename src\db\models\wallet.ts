import { and, eq, type InferSelectModel } from "drizzle-orm"
import { walletSchema } from "../schema/wallet"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"
import { db } from ".."
import { BlockchainUtils } from "../../blockchain/utils"
import { createId } from "@paralleldrive/cuid2"

export class WalletModel {
  private static name = `WalletModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  // COMING SOON !!!!!!!!!!
  static type: InferSelectModel<typeof walletSchema>

  /**
   * Create a new wallet for a user
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name (must be unique per user)
   * @param chain Blockchain network for this wallet
   * @param chainId Chain-specific identifier
   * @param address Wallet address
   * @param privateKey Privatekey for the wallet (will be encrypted before storage)
   * @param createdBy How the wallet was created (`system` or `import`)
   * @param encryptionPassword Password to encrypt the private key with
   * @returns The created wallet, null if creation failed, or `DUPLICATE_NAME` if name exists, or `DUPLICATE_ADDRESS` if address exists
   */
  static async create(ownerId: number, name: string, chain: string, chainId: number, address: string, privateKey: string, createdBy: `system` | `import` = `system`, encryptionPassword: string) {
    try {
      // Check if wallet name already exists for this user
      const existingWallet = await this.getByName(ownerId, name)
      if (existingWallet) {
        return `DUPLICATE_NAME` // Wallet name already exists
      }

      // Check if wallet address already exists for this chain
      const existingAddressWallet = await this.getByAddressAndChain(ownerId, address, chain)
      if (existingAddressWallet) {
        return `DUPLICATE_ADDRESS` // Wallet address already exists for this chain
      }

      // Encrypt the private key before storing
      const encryptedPrivateKey = BlockchainUtils.encryptPrivateKey(privateKey, encryptionPassword)

      // Create new wallet
      await db
        .insert(walletSchema)
        .values({
          id: createId(),
          owner: ownerId,
          name,
          chain,
          chainId,
          address,
          balance: BigInt(0),
          privateKey: encryptedPrivateKey,
          createdBy
        })
        .execute()

      // Get the created wallet
      return await this.getByName(ownerId, name)
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /**
   * Get a wallet by ID with ownership validation (SECURE)
   * @param walletId Wallet ID
   * @param ownerId User ID (owner of the wallet) - ensures user can only access their own wallets
   * @returns The wallet or null if not found or not owned by the user
   */
  static async getByIdForOwner(walletId: string, ownerId: number) {
    try {
      const wallet = await db
        .select()
        .from(walletSchema)
        .where(and(eq(walletSchema.id, walletId), eq(walletSchema.owner, ownerId)))
        .limit(1)
      return wallet[0] || null
    } catch (error) {
      return this.logError(error, `getByIdForOwner`)
    }
  }

  /**
   * Get a wallet by name for a specific user
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name
   * @returns The wallet or null if not found
   */
  static async getByName(ownerId: number, name: string) {
    try {
      const [row] = await db
        .select()
        .from(walletSchema)
        .where(and(eq(walletSchema.owner, ownerId), eq(walletSchema.name, name)))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByName`)
    }
  }

  /**
   * Get all wallets for an owner
   * @param ownerId User ID (owner of the wallets)
   * @returns Array of wallets
   */
  static async getAllForOwner(ownerId: number) {
    try {
      return await db.select().from(walletSchema).where(eq(walletSchema.owner, ownerId))
    } catch (error) {
      return this.logError(error, `getAllForOwner`, [])
    }
  }

  /**
   * Import a wallet with a privatekey
   * @param ownerId User ID (owner of the wallet)
   * @param name Wallet name
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier
   * @param address Wallet address
   * @param privateKey Privatekey for the wallet (will be encrypted before storage)
   * @param encryptionPassword Password to encrypt the private key with
   * @returns The imported wallet, null if import failed, or `DUPLICATE_NAME` if name exists, or `DUPLICATE_ADDRESS` if address exists
   */
  static async import(ownerId: number, name: string, chain: string, chainId: number, address: string, privateKey: string, encryptionPassword: string) {
    try {
      // Check if wallet name already exists for this user
      const existingWallet = await this.getByName(ownerId, name)
      if (existingWallet) {
        return `DUPLICATE_NAME` // Wallet name already exists
      }

      // Check if wallet address already exists for this chain
      const existingAddressWallet = await this.getByAddressAndChain(ownerId, address, chain)
      if (existingAddressWallet) {
        return `DUPLICATE_ADDRESS` // Wallet address already exists for this chain
      }

      // Encrypt the private key before storing
      const encryptedPrivateKey = BlockchainUtils.encryptPrivateKey(privateKey, encryptionPassword)

      // Create new wallet with import flag
      await db
        .insert(walletSchema)
        .values({
          id: createId(),
          owner: ownerId,
          name,
          chain,
          chainId,
          address,
          balance: BigInt(0),
          privateKey: encryptedPrivateKey,
          createdBy: `import`
        })
        .execute()

      // Get the created wallet
      return this.getByName(ownerId, name)
    } catch (error) {
      return this.logError(error, `import`)
    }
  }

  /**
   * Remove a wallet
   * @param walletId Wallet ID
   * @param ownerId User ID (for security verification)
   * @returns True if removal was successful, false otherwise
   */
  static async remove(walletId: string, ownerId: number) {
    try {
      // Delete the wallet
      await db.delete(walletSchema).where(and(eq(walletSchema.id, walletId), eq(walletSchema.owner, ownerId)))
      return true
    } catch (error) {
      return this.logError(error, `remove`, false)
    }
  }

  /**
   * Update wallet balance (SECURE - with ownership validation)
   * @param walletId Wallet ID
   * @param ownerId User ID (owner of the wallet) - ensures user can only update their own wallets
   * @param balance New balance amount
   * @returns Updated wallet or null if operation failed or wallet not owned by user
   */
  static async updateBalance(walletId: string, ownerId: number, balance: bigint) {
    try {
      // First verify ownership
      const wallet = await this.getByIdForOwner(walletId, ownerId)
      if (!wallet) {
        return null
      }

      // Update the balance with ownership constraint
      await db
        .update(walletSchema)
        .set({ balance })
        .where(and(eq(walletSchema.id, walletId), eq(walletSchema.owner, ownerId)))
        .execute()

      return this.getByIdForOwner(walletId, ownerId)
    } catch (error) {
      return this.logError(error, `updateBalance`)
    }
  }

  /**
   * Get wallets by chain type
   * @param ownerId User ID (owner of the wallets)
   * @param chain Blockchain network to filter by
   * @returns Array of wallets for the specified chain
   */
  static async getByChain(ownerId: number, chain: string) {
    try {
      return await db
        .select()
        .from(walletSchema)
        .where(and(eq(walletSchema.owner, ownerId), eq(walletSchema.chain, chain)))
    } catch (error) {
      return this.logError(error, `getByChain`, [])
    }
  }

  /**
   * Get wallet by address and chain combination
   * @param ownerId User ID (owner of the wallet)
   * @param address Wallet address
   * @param chain Blockchain network
   * @returns The wallet or null if not found
   */
  static async getByAddressAndChain(ownerId: number, address: string, chain: string) {
    try {
      const [row] = await db
        .select()
        .from(walletSchema)
        .where(and(eq(walletSchema.owner, ownerId), eq(walletSchema.address, address), eq(walletSchema.chain, chain)))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByAddressAndChain`)
    }
  }

  /**
   * Calculate total balance for a user across all wallets for a specific chain
   * @param ownerId User ID
   * @param chain Blockchain network
   * @returns Total balance as a BigInt or null if operation failed
   */
  static async calculateTotalBalanceByChain(ownerId: number, chain: string) {
    try {
      const wallets = await this.getByChain(ownerId, chain)

      // Calculate total balance for the specified chain
      let totalBalance = BigInt(0)
      for (const wallet of wallets) {
        totalBalance += wallet.balance
      }

      return totalBalance
    } catch (error) {
      return this.logError(error, `calculateTotalBalanceByChain`)
    }
  }

  /**
   * Get wallet count by chain for a user
   * @param ownerId User ID
   * @returns Object with chain counts
   */
  static async getWalletCountsByChain(ownerId: number) {
    try {
      const wallets = await this.getAllForOwner(ownerId)
      const counts: Record<string, number> = {}

      // Initialize counts for all chains
      BlockchainConfig.listChainSymbol.forEach((chain) => {
        counts[chain] = 0
      })

      // Count wallets for each chain
      for (const wallet of wallets) {
        counts[wallet.chain] = (counts[wallet.chain] || 0) + 1
      }

      return counts
    } catch (error) {
      return this.logError(error, `getWalletCountsByChain`)
    }
  }
}
