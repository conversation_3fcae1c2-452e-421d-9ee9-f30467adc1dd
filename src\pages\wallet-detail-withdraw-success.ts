import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { ModelTrades } from "../db/models"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawSuccessPage {
  private name = Name.walletDetailWithdrawSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletId: bigint) {
    const hex = BlockchainUtils.toBeHex(walletId)
    return new InlineKeyboard().text(`💼 My Wallets`, Name.wallet).text(`📊 View History`, `${Name.walletDetailPosition}:${hex}`).row().text(`... Back`, `${Name.walletDetail}:${hex}`)
  }

  async show(sessionParams: Record<any, any>, wallet: any, txHash: string, actualFee: any, newBalance: bigint) {
    const { chainName, toAddress, amount } = sessionParams
    const { chainDisplayName, chainSymbol, explorerUrl } = BlockchainConfig.get(chainName)
    await this.handler.sessionDelete()

    // Record successful transaction in history
    await ModelTrades.create(
      BigInt(wallet.id),
      chainName,
      true, // success = true
      amount,
      `S` // S for send/withdraw
    )

    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      toAddress,
      amount,
      chainSymbol,
      actualFee,
      txHash,
      explorerUrl,
      newBalance: newBalance.toString()
    })
  }
}
