import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportWarnPrivateKeyInvalidPage {
  private name = Name.walletImportWarnPrivateKeyInvalid
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(WalletImportWarnPrivateKeyInvalidPage.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.walletImport)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard)
  }
}
